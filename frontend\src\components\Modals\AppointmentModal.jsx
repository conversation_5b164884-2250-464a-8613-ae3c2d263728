import { useState, useEffect, useCallback, useRef } from 'react'
import { FiX, FiCalendar, FiUser, FiPhone, FiMail, FiClock, FiSearch } from 'react-icons/fi'
import { adminService } from '../../services'

const AppointmentModal = ({ 
  isOpen, 
  onClose, 
  onSave, 
  editingItem, 
  modalType,
  branding 
}) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    customerEmail: '',
    customerPhone: '',
    userId: '',
    service: '',
    date: '',
    time: '',
    notes: '',
    status: 'scheduled'
  })

  const [errors, setErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Customer search states
  const [customerSearchTerm, setCustomerSearchTerm] = useState('')
  const [customers, setCustomers] = useState([])
  const [isSearching, setIsSearching] = useState(false)
  const [showCustomerDropdown, setShowCustomerDropdown] = useState(false)
  const [selectedCustomer, setSelectedCustomer] = useState(null)
  const searchTimeoutRef = useRef(null)
  const dropdownRef = useRef(null)

  // Debounced customer search
  const searchCustomers = useCallback(async (searchTerm) => {
    if (!searchTerm.trim()) {
      setCustomers([])
      setIsSearching(false)
      return
    }

    setIsSearching(true)
    try {
      const response = await adminService.getCustomers({
        search: searchTerm,
        limit: 10
      })

      if (response.success) {
        setCustomers(response.data.customers || [])
      }
    } catch (error) {
      console.error('Error searching customers:', error)
      setCustomers([])
    } finally {
      setIsSearching(false)
    }
  }, [])

  // Debounced search effect
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current)
    }

    searchTimeoutRef.current = setTimeout(() => {
      searchCustomers(customerSearchTerm)
    }, 300)

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current)
      }
    }
  }, [customerSearchTerm, searchCustomers])

  // Click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowCustomerDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Reset form when modal opens/closes or editing item changes
  useEffect(() => {
    if (isOpen) {
      if (editingItem && modalType === 'edit') {
        // Split customerName if it exists for backward compatibility
        const nameParts = (editingItem.customerName || '').split(' ')
        setFormData({
          firstName: nameParts[0] || '',
          lastName: nameParts.slice(1).join(' ') || '',
          customerEmail: editingItem.customerEmail || '',
          customerPhone: editingItem.customerPhone || '',
          userId: editingItem.userId || '',
          service: editingItem.service || '',
          date: editingItem.date || '',
          time: editingItem.time || '',
          notes: editingItem.notes || '',
          status: editingItem.status || 'scheduled'
        })

        // Set selected customer for editing
        if (editingItem.userId) {
          setSelectedCustomer({
            _id: editingItem.userId,
            name: editingItem.customerName || `${editingItem.firstName || ''} ${editingItem.lastName || ''}`.trim(),
            email: editingItem.customerEmail || '',
            phone: editingItem.customerPhone || ''
          })
          setCustomerSearchTerm(editingItem.customerName || `${editingItem.firstName || ''} ${editingItem.lastName || ''}`.trim())
        }
      } else {
        setFormData({
          firstName: '',
          lastName: '',
          customerEmail: '',
          customerPhone: '',
          userId: '',
          service: '',
          date: '',
          time: '',
          notes: '',
          status: 'scheduled'
        })
        setSelectedCustomer(null)
        setCustomerSearchTerm('')
      }
      setErrors({})
      setShowCustomerDropdown(false)
    }
  }, [isOpen, editingItem, modalType])

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  // Handle customer selection
  const handleCustomerSelect = (customer) => {
    setSelectedCustomer(customer)
    setCustomerSearchTerm(`${customer.firstName || ''} ${customer.lastName || ''}`.trim() || customer.name || customer.email)
    setFormData(prev => ({
      ...prev,
      userId: customer._id,
      firstName: customer.firstName || customer.name?.split(' ')[0] || '',
      lastName: customer.lastName || customer.name?.split(' ').slice(1).join(' ') || '',
      customerEmail: customer.email || '',
      customerPhone: customer.phone || ''
    }))
    setShowCustomerDropdown(false)

    // Clear userId error if it exists
    if (errors.userId) {
      setErrors(prev => ({
        ...prev,
        userId: ''
      }))
    }
  }

  // Handle customer search input change
  const handleCustomerSearchChange = (value) => {
    setCustomerSearchTerm(value)
    setShowCustomerDropdown(true)

    // If user clears the search, clear the selected customer
    if (!value.trim()) {
      setSelectedCustomer(null)
      setFormData(prev => ({
        ...prev,
        userId: '',
        firstName: '',
        lastName: '',
        customerEmail: '',
        customerPhone: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required'
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required'
    }
    if (!formData.userId.trim()) {
      newErrors.userId = 'Customer selection is required'
    }
    if (!formData.customerEmail.trim()) {
      newErrors.customerEmail = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.customerEmail)) {
      newErrors.customerEmail = 'Please enter a valid email'
    }
    if (!formData.customerPhone.trim()) {
      newErrors.customerPhone = 'Phone number is required'
    }
    if (!formData.service.trim()) {
      newErrors.service = 'Service is required'
    }
    if (!formData.date) {
      newErrors.date = 'Date is required'
    }
    if (!formData.time) {
      newErrors.time = 'Time is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    try {
      await onSave(formData)
      onClose()
    } catch (error) {
      console.error('Error saving appointment:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!isOpen) return null

  const services = [
    'Hair Cut & Style',
    'Hair Color',
    'Hair Treatment',
    'Braiding',
    'Extensions',
    'Consultation'
  ]

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[70] p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">
            {modalType === 'edit' ? 'Edit Appointment' : 'Add New Appointment'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
          >
            <FiX className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Customer Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <FiUser className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Customer Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    First Name *
                  </label>
                  <input
                    type="text"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.firstName ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter first name"
                  />
                  {errors.firstName && (
                    <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Last Name *
                  </label>
                  <input
                    type="text"
                    value={formData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.lastName ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter last name"
                  />
                  {errors.lastName && (
                    <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number *
                </label>
                <input
                  type="tel"
                  value={formData.customerPhone}
                  onChange={(e) => handleInputChange('customerPhone', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.customerPhone ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter phone number"
                />
                {errors.customerPhone && (
                  <p className="text-red-500 text-sm mt-1">{errors.customerPhone}</p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address *
              </label>
              <input
                type="email"
                value={formData.customerEmail}
                onChange={(e) => handleInputChange('customerEmail', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.customerEmail ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter email address"
              />
              {errors.customerEmail && (
                <p className="text-red-500 text-sm mt-1">{errors.customerEmail}</p>
              )}
            </div>

            <div className="relative" ref={dropdownRef}>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search Customer *
              </label>
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  value={customerSearchTerm}
                  onChange={(e) => handleCustomerSearchChange(e.target.value)}
                  onFocus={() => setShowCustomerDropdown(true)}
                  className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.userId ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Search by name, email, or phone..."
                />
                {isSearching && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  </div>
                )}
              </div>

              {/* Customer dropdown */}
              {showCustomerDropdown && customerSearchTerm && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                  {customers.length > 0 ? (
                    customers.map((customer) => (
                      <button
                        key={customer._id}
                        type="button"
                        onClick={() => handleCustomerSelect(customer)}
                        className="w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-100 last:border-b-0"
                      >
                        <div className="font-medium text-gray-900">
                          {`${customer.firstName || ''} ${customer.lastName || ''}`.trim() || customer.name || 'No Name'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {customer.email} {customer.phone && `• ${customer.phone}`}
                        </div>
                      </button>
                    ))
                  ) : !isSearching && customerSearchTerm ? (
                    <div className="px-3 py-2 text-gray-500 text-sm">
                      No customers found. You can still create the appointment with the entered details.
                    </div>
                  ) : null}
                </div>
              )}

              {errors.userId && (
                <p className="text-red-500 text-sm mt-1">{errors.userId}</p>
              )}
              <p className="text-sm text-gray-500 mt-1">
                Start typing to search for existing customers or enter new customer details below
              </p>
            </div>
          </div>

          {/* Appointment Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <FiCalendar className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Appointment Details
            </h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Service *
              </label>
              <select
                value={formData.service}
                onChange={(e) => handleInputChange('service', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.service ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Select a service</option>
                {services.map(service => (
                  <option key={service} value={service}>{service}</option>
                ))}
              </select>
              {errors.service && (
                <p className="text-red-500 text-sm mt-1">{errors.service}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Date *
                </label>
                <input
                  type="date"
                  value={formData.date}
                  onChange={(e) => handleInputChange('date', e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.date ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.date && (
                  <p className="text-red-500 text-sm mt-1">{errors.date}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Time *
                </label>
                <input
                  type="time"
                  value={formData.time}
                  onChange={(e) => handleInputChange('time', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.time ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.time && (
                  <p className="text-red-500 text-sm mt-1">{errors.time}</p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="scheduled">Scheduled</option>
                <option value="confirmed">Confirmed</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Notes
              </label>
              <textarea
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                placeholder="Additional notes or special requests..."
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-6 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Saving...' : (modalType === 'edit' ? 'Update Appointment' : 'Create Appointment')}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default AppointmentModal
