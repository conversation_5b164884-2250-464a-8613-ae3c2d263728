import { Router } from 'express';
import { Admin<PERSON>ontroller, ProductController, ServiceController } from '../controllers';
import { authenticate, authorize } from '../middleware/auth';
import { validate } from '../middleware/validation';
import { paginationValidation, mongoIdValidation } from '../utils/validation';

const router = Router();

// All admin routes require authentication and admin role
router.use(authenticate);
router.use(authorize('admin'));

// GET /api/admin/dashboard
router.get(
  '/dashboard',
  AdminController.getDashboardStats
);

// GET /api/admin/appointments
router.get(
  '/appointments',
  validate(paginationValidation),
  AdminController.getAppointments
);

// POST /api/admin/appointments
router.post(
  '/appointments',
  AdminController.createAppointment
);

// GET /api/admin/customers
router.get(
  '/customers',
  validate(paginationValidation),
  AdminController.getCustomers
);

// GET /api/admin/orders
router.get(
  '/orders',
  validate(paginationValidation),
  AdminController.getOrders
);

// GET /api/admin/products
router.get(
  '/products',
  validate(paginationValidation),
  AdminController.getProducts
);

// POST /api/admin/products
router.post(
  '/products',
  ProductController.createProduct
);

// PUT /api/admin/products/:id
router.put(
  '/products/:id',
  validate(mongoIdValidation()),
  ProductController.updateProduct
);

// POST /api/admin/services
router.post(
  '/services',
  ServiceController.createService
);

// PUT /api/admin/services/:id
router.put(
  '/services/:id',
  validate(mongoIdValidation()),
  ServiceController.updateService
);

export default router;
