import React, { useState } from 'react'
import { FiX, FiEdit3, FiTrash2, FiPackage, FiDollarSign, FiTag, FiImage, FiBarChart3, FiCalendar } from 'react-icons/fi'
import { useBranding } from '../../contexts/BrandingContext'

const ProductDetailModal = ({ 
  product, 
  isOpen, 
  onClose, 
  onEdit, 
  onDelete,
  showToast 
}) => {
  const { branding } = useBranding()
  const [isDeleting, setIsDeleting] = useState(false)

  if (!isOpen || !product) return null

  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this product?')) return
    
    setIsDeleting(true)
    try {
      await onDelete(product._id)
      showToast('Product deleted successfully', 'success')
      onClose()
    } catch (error) {
      showToast('Failed to delete product', 'error')
    } finally {
      setIsDeleting(false)
    }
  }

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive': return 'bg-red-100 text-red-800 border-red-200'
      case 'draft': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'out_of_stock': return 'bg-orange-100 text-orange-800 border-orange-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount || 0)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div 
              className="w-10 h-10 rounded-lg flex items-center justify-center"
              style={{ backgroundColor: branding?.colors?.primary + '20' || '#3B82F620' }}
            >
              <FiPackage className="w-5 h-5" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Product Details</h2>
              <p className="text-sm text-gray-500">
                SKU: {product.sku || product._id?.slice(-8) || 'N/A'}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
          >
            <FiX className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Status and Actions */}
          <div className="flex justify-between items-start">
            <span className={`px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full border ${getStatusColor(product.status)}`}>
              {product.status || 'Draft'}
            </span>
            <div className="flex space-x-2">
              <button
                onClick={() => onEdit(product)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center space-x-2"
              >
                <FiEdit3 className="w-4 h-4" />
                <span>Edit</span>
              </button>
              <button
                onClick={handleDelete}
                disabled={isDeleting}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 flex items-center space-x-2 disabled:opacity-50"
              >
                <FiTrash2 className="w-4 h-4" />
                <span>{isDeleting ? 'Deleting...' : 'Delete'}</span>
              </button>
            </div>
          </div>

          {/* Product Images */}
          {product.images && product.images.length > 0 && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
                <FiImage className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
                Product Images ({product.images.length})
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {product.images.map((image, index) => (
                  <div key={index} className="aspect-square bg-white rounded-lg border overflow-hidden">
                    <img 
                      src={image} 
                      alt={`${product.name} - Image ${index + 1}`}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.target.src = '/placeholder-image.png'
                      }}
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Basic Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
              <FiPackage className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Basic Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-600 mb-1">Product Name</label>
                <div className="text-gray-900 font-medium text-lg">
                  {product.name || 'N/A'}
                </div>
              </div>
              {product.description && (
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-600 mb-1">Description</label>
                  <div className="text-gray-900 bg-white p-3 rounded border">
                    {product.description}
                  </div>
                </div>
              )}
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Category</label>
                <div className="text-gray-900">
                  {product.category || 'Uncategorized'}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Brand</label>
                <div className="text-gray-900">
                  {product.brand || 'N/A'}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">SKU</label>
                <div className="text-gray-900 font-mono">
                  {product.sku || 'N/A'}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Barcode</label>
                <div className="text-gray-900 font-mono">
                  {product.barcode || 'N/A'}
                </div>
              </div>
            </div>
          </div>

          {/* Pricing Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
              <FiDollarSign className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Pricing Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Regular Price</label>
                <div className="text-gray-900 font-bold text-lg">
                  {formatCurrency(product.price)}
                </div>
              </div>
              {product.salePrice && (
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Sale Price</label>
                  <div className="text-green-600 font-bold text-lg">
                    {formatCurrency(product.salePrice)}
                  </div>
                </div>
              )}
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Cost Price</label>
                <div className="text-gray-900">
                  {formatCurrency(product.costPrice)}
                </div>
              </div>
              {product.compareAtPrice && (
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Compare At Price</label>
                  <div className="text-gray-500 line-through">
                    {formatCurrency(product.compareAtPrice)}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Inventory Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
              <FiBarChart3 className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Inventory Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Stock Quantity</label>
                <div className={`text-lg font-semibold ${
                  product.stock > 10 ? 'text-green-600' : 
                  product.stock > 0 ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {product.stock || 0} units
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Low Stock Alert</label>
                <div className="text-gray-900">
                  {product.lowStockAlert || 'Not set'}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Track Inventory</label>
                <div>
                  <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    product.trackInventory 
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {product.trackInventory ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
              {product.weight && (
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Weight</label>
                  <div className="text-gray-900">
                    {product.weight} {product.weightUnit || 'lbs'}
                  </div>
                </div>
              )}
              {product.dimensions && (
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Dimensions</label>
                  <div className="text-gray-900">
                    {product.dimensions}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Tags and Categories */}
          {(product.tags && product.tags.length > 0) && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
                <FiTag className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
                Tags
              </h3>
              <div className="flex flex-wrap gap-2">
                {product.tags.map((tag, index) => (
                  <span 
                    key={index}
                    className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Product Timeline */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
              <FiCalendar className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Product Timeline
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Created Date</label>
                <div className="text-gray-900">
                  {product.createdAt ? new Date(product.createdAt).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  }) : 'N/A'}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Last Updated</label>
                <div className="text-gray-900">
                  {product.updatedAt ? new Date(product.updatedAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  }) : 'N/A'}
                </div>
              </div>
            </div>
          </div>

          {/* Additional Information */}
          {(product.metaTitle || product.metaDescription || product.seoKeywords) && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                SEO Information
              </h3>
              <div className="space-y-3">
                {product.metaTitle && (
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Meta Title</label>
                    <div className="text-gray-900">{product.metaTitle}</div>
                  </div>
                )}
                {product.metaDescription && (
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Meta Description</label>
                    <div className="text-gray-900">{product.metaDescription}</div>
                  </div>
                )}
                {product.seoKeywords && (
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">SEO Keywords</label>
                    <div className="text-gray-900">{product.seoKeywords}</div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-4 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  )
}

export default ProductDetailModal
