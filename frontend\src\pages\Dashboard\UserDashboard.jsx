import { useState, useEffect, useCallback, useRef } from 'react'
import {
  FiCalendar,
  FiShoppingBag,
  FiUser,
  FiHeart,
  FiClock,
  FiEdit3,
  FiEye,
  FiAlertCircle,
  FiMenu,
  FiX,
  FiLogOut
} from 'react-icons/fi'
import {
  userService,
  appointmentService,
  orderService,
  cartService,
  notificationService
} from '../../services'
import { FALLBACK_IMAGES } from '../../utils/constants'
import { useBranding } from '../../contexts/BrandingContext'
import { useRouter } from '../../hooks/useRouter'
import Loading from '../../components/Loading'
import AppointmentModal from '../../components/Modals/AppointmentModal'

const UserDashboard = ({ onNavigate, onLogout }) => {
  const { branding } = useBranding()
  const { parseCurrentRoute, navigateToSubRoute } = useRouter()

  // Initialize activeTab from URL if available
  const [activeTab, setActiveTab] = useState(() => {
    const { mainRoute, subRoute } = parseCurrentRoute()
    return (mainRoute === 'user-dashboard' && subRoute) ? subRoute : 'overview'
  })

  const [userData, setUserData] = useState(null)
  const [appointments, setAppointments] = useState([])
  const [orders, setOrders] = useState([])
  const [favorites, setFavorites] = useState([])
  const [notifications, setNotifications] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [isUpdating, setIsUpdating] = useState(false)
  const [sectionLoading, setSectionLoading] = useState({
    appointments: false,
    orders: false,
    favorites: false,
    notifications: false
  })

  // Modal states for appointment booking
  const [showAppointmentModal, setShowAppointmentModal] = useState(false)
  const [modalType, setModalType] = useState('add')
  const [editingAppointment, setEditingAppointment] = useState(null)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [profileData, setProfileData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: ''
  })
  const loadingRef = useRef(false)





  // Track which data has been loaded
  const [loadedData, setLoadedData] = useState({
    profile: false,
    appointments: false,
    orders: false,
    favorites: false,
    notifications: false
  })

  // Load user profile (always needed)
  const loadUserProfile = useCallback(async () => {
    if (loadedData.profile || loadingRef.current) return

    try {
      loadingRef.current = true
      const response = await userService.getProfile()

      if (response.success) {
        const user = response.data
        setUserData(user)
        setProfileData({
          firstName: user.firstName || '',
          lastName: user.lastName || '',
          email: user.email || '',
          phone: user.phone || ''
        })
        setLoadedData(prev => ({ ...prev, profile: true }))
      }
    } catch (error) {
      console.error('Error loading profile:', error)
      setError('Failed to load profile data.')
    } finally {
      loadingRef.current = false
    }
  }, [loadedData.profile])

  // Load appointments data
  const loadAppointments = useCallback(async () => {
    if (loadedData.appointments) return

    try {
      setSectionLoading(prev => ({ ...prev, appointments: true }))
      const response = await appointmentService.getUserAppointments()

      if (response.success) {
        setAppointments(response.data)
        setLoadedData(prev => ({ ...prev, appointments: true }))
      }
    } catch (error) {
      console.error('Error loading appointments:', error)
      if (activeTab === 'appointments') {
        setError('Failed to load appointments.')
      }
    } finally {
      setSectionLoading(prev => ({ ...prev, appointments: false }))
    }
  }, [loadedData.appointments, activeTab])

  // Load orders data
  const loadOrders = useCallback(async () => {
    if (loadedData.orders) return

    try {
      setSectionLoading(prev => ({ ...prev, orders: true }))
      const response = await orderService.getUserOrders()

      if (response.success) {
        setOrders(response.data)
        setLoadedData(prev => ({ ...prev, orders: true }))
      }
    } catch (error) {
      console.error('Error loading orders:', error)
      if (activeTab === 'orders') {
        setError('Failed to load orders.')
      }
    } finally {
      setSectionLoading(prev => ({ ...prev, orders: false }))
    }
  }, [loadedData.orders, activeTab])

  // Load favorites data
  const loadFavorites = useCallback(async () => {
    if (loadedData.favorites) return

    try {
      setSectionLoading(prev => ({ ...prev, favorites: true }))
      const response = await userService.getFavorites()

      if (response.success) {
        setFavorites(response.data)
        setLoadedData(prev => ({ ...prev, favorites: true }))
      }
    } catch (error) {
      console.error('Error loading favorites:', error)
      if (activeTab === 'favorites') {
        setError('Failed to load favorites.')
      }
    } finally {
      setSectionLoading(prev => ({ ...prev, favorites: false }))
    }
  }, [loadedData.favorites, activeTab])

  // Load notifications for overview
  const loadNotifications = useCallback(async () => {
    if (loadedData.notifications) return

    try {
      setSectionLoading(prev => ({ ...prev, notifications: true }))
      const response = await notificationService.getNotifications({ limit: 5 })

      if (response.success) {
        setNotifications(response.data)
        setLoadedData(prev => ({ ...prev, notifications: true }))
      }
    } catch (error) {
      console.error('Error loading notifications:', error)
    } finally {
      setSectionLoading(prev => ({ ...prev, notifications: false }))
    }
  }, [loadedData.notifications])

  // Load data based on active tab
  const loadDataForTab = useCallback((tabId) => {
    switch (tabId) {
      case 'overview':
        // Overview needs appointments and notifications for recent activity
        loadAppointments()
        loadNotifications()
        break
      case 'appointments':
        loadAppointments()
        break
      case 'orders':
        loadOrders()
        break
      case 'favorites':
        loadFavorites()
        break
      case 'profile':
        // Profile data is already loaded with user profile
        break
      default:
        break
    }
  }, [loadAppointments, loadNotifications, loadOrders, loadFavorites])

  // Update URL when tab changes and load required data
  const handleTabChange = (tabId) => {
    setActiveTab(tabId)
    // Don't replace history - allow proper back navigation
    navigateToSubRoute('user-dashboard', tabId, { replace: false, scrollToTop: false })

    // Load data based on the selected tab
    loadDataForTab(tabId)
  }

  // Load initial data on component mount
  useEffect(() => {
    const initializeDashboard = async () => {
      setIsLoading(true)
      setError('')

      try {
        // Always load user profile first
        await loadUserProfile()

        // Set initial URL if no sub-route is present
        const { mainRoute, subRoute } = parseCurrentRoute()
        if (mainRoute === 'user-dashboard' && !subRoute) {
          navigateToSubRoute('user-dashboard', 'overview', { replace: true, scrollToTop: false })
          // Load data for overview tab
          loadDataForTab('overview')
        } else if (subRoute) {
          // Load data for the current tab
          loadDataForTab(subRoute)
        }
      } catch (error) {
        console.error('Error initializing dashboard:', error)
        setError('Failed to load dashboard. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    initializeDashboard()
  }, [loadUserProfile, parseCurrentRoute, navigateToSubRoute, loadDataForTab])

  // Load data when tab changes via browser navigation
  useEffect(() => {
    const { mainRoute, subRoute } = parseCurrentRoute()
    if (mainRoute === 'user-dashboard') {
      const newTab = subRoute || 'overview'
      if (newTab !== activeTab) {
        setActiveTab(newTab)
        loadDataForTab(newTab)
      }
    }
  }, [parseCurrentRoute, activeTab, loadDataForTab])

  // Close mobile menu when screen size changes to desktop
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) { // lg breakpoint
        setIsMobileMenuOpen(false)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Helper functions
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'confirmed':
      case 'scheduled':
        return 'bg-blue-100 text-blue-800'
      case 'completed':
      case 'delivered':
        return 'bg-green-100 text-green-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'processing':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // CRUD handlers for appointments
  const handleEditAppointment = (appointment) => {
    setEditingAppointment(appointment)
    setModalType('edit')
    setShowAppointmentModal(true)
  }

  const handleViewAppointment = (appointment) => {
    setEditingAppointment(appointment)
    setModalType('view')
    setShowAppointmentModal(true)
  }

  const handleCancelAppointment = async (appointment) => {
    if (!window.confirm('Are you sure you want to cancel this appointment?')) {
      return
    }

    try {
      const response = await appointmentService.cancelAppointment(appointment._id || appointment.id)

      if (response.success) {
        // Update local state
        setAppointments(prev => prev.filter(apt => (apt._id || apt.id) !== (appointment._id || appointment.id)))

        // Show success message
        window.dispatchEvent(new CustomEvent('show-notification', {
          detail: {
            type: 'success',
            message: 'Appointment cancelled successfully!'
          }
        }))
      } else {
        window.dispatchEvent(new CustomEvent('show-notification', {
          detail: {
            type: 'error',
            message: 'Failed to cancel appointment: ' + (response.message || 'Unknown error')
          }
        }))
      }
    } catch (error) {
      console.error('Error cancelling appointment:', error)
      window.dispatchEvent(new CustomEvent('show-notification', {
        detail: {
          type: 'error',
          message: 'Failed to cancel appointment. Please try again.'
        }
      }))
    }
  }

  // Handle appointment save
  const handleSaveAppointment = async (appointmentData) => {
    try {
      let response

      if (modalType === 'edit' && editingAppointment) {
        // Update existing appointment
        response = await appointmentService.updateAppointment(
          editingAppointment._id || editingAppointment.id,
          appointmentData
        )

        if (response.success) {
          // Update local state
          setAppointments(prev => prev.map(apt =>
            (apt._id || apt.id) === (editingAppointment._id || editingAppointment.id)
              ? { ...apt, ...response.data }
              : apt
          ))

          window.dispatchEvent(new CustomEvent('show-notification', {
            detail: {
              type: 'success',
              message: 'Appointment updated successfully!'
            }
          }))
        }
      } else {
        // Create new appointment
        response = await appointmentService.createAppointment(appointmentData)

        if (response.success) {
          // Add to local state
          setAppointments(prev => [response.data, ...prev])

          window.dispatchEvent(new CustomEvent('show-notification', {
            detail: {
              type: 'success',
              message: 'Appointment booked successfully!'
            }
          }))
        }
      }

      if (response.success) {
        setShowAppointmentModal(false)
        setEditingAppointment(null)
        setModalType('add')
      } else {
        window.dispatchEvent(new CustomEvent('show-notification', {
          detail: {
            type: 'error',
            message: 'Failed to save appointment: ' + (response.message || 'Unknown error')
          }
        }))
      }
    } catch (error) {
      console.error('Error saving appointment:', error)
      window.dispatchEvent(new CustomEvent('show-notification', {
        detail: {
          type: 'error',
          message: error.message || 'Failed to save appointment. Please try again.'
        }
      }))
    }
  }

  const formatDate = (dateString) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    } catch (error) {
      return dateString
    }
  }

  const getNextAppointment = () => {
    const upcoming = appointments.filter(apt =>
      new Date(apt.date) > new Date() &&
      (apt.status === 'confirmed' || apt.status === 'scheduled')
    ).sort((a, b) => new Date(a.date) - new Date(b.date))

    return upcoming[0]
  }

  const addToCart = async (productId) => {
    try {
      setIsUpdating(true)
      const response = await cartService.addToCart({
        productId,
        quantity: 1
      })

      if (response.success) {
        window.dispatchEvent(new CustomEvent('show-notification', {
          detail: {
            type: 'success',
            message: 'Product added to cart!'
          }
        }))
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
      window.dispatchEvent(new CustomEvent('show-notification', {
        detail: {
          type: 'error',
          message: 'Failed to add product to cart'
        }
      }))
    } finally {
      setIsUpdating(false)
    }
  }

  const handleProfileChange = (field, value) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleProfileSave = async () => {
    try {
      setIsUpdating(true)
      const response = await userService.updateProfile(profileData)

      if (response.success) {
        setUserData(prev => ({ ...prev, ...profileData }))
        window.dispatchEvent(new CustomEvent('show-notification', {
          detail: {
            type: 'success',
            message: 'Profile updated successfully!'
          }
        }))
      }
    } catch (error) {
      console.error('Error updating profile:', error)
      window.dispatchEvent(new CustomEvent('show-notification', {
        detail: {
          type: 'error',
          message: 'Failed to update profile'
        }
      }))
    } finally {
      setIsUpdating(false)
    }
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: FiUser },
    { id: 'appointments', name: branding.content.dashboardAppointmentsTitle, icon: FiCalendar },
    { id: 'orders', name: branding.content.dashboardOrdersTitle, icon: FiShoppingBag },
    { id: 'favorites', name: branding.content.dashboardFavoritesTitle, icon: FiHeart },
    { id: 'profile', name: branding.content.dashboardProfileTitle, icon: FiEdit3 }
  ]

  const renderOverview = () => {
    const nextAppointment = getNextAppointment()

    return (
      <div className="space-y-4 lg:space-y-6">
        {/* Welcome Section */}
        <div className="relative rounded-2xl p-6 text-white overflow-hidden transform hover:scale-[1.01] transition-all duration-300 shadow-xl border border-white/20"
             style={{ background: 'linear-gradient(135deg, #008000, #006600, #004d00)' }}>
          {/* Glassmorphism overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-white/5"></div>
          {/* Floating orbs */}
          <div className="absolute top-3 right-3 w-16 h-16 bg-white/10 rounded-full blur-xl"></div>
          <div className="absolute bottom-3 left-3 w-12 h-12 bg-white/5 rounded-full blur-lg"></div>

          <div className="relative z-10">
            <h2 className="text-xl lg:text-2xl font-bold mb-2 bg-gradient-to-r from-white to-green-100 bg-clip-text text-transparent">
              {branding.content.dashboardWelcome.replace('Welcome back', `Welcome back, ${userData?.firstName || userData?.name || 'User'}`)}
            </h2>
            <p className="text-green-100 text-sm lg:text-base font-medium">{branding.content.dashboardOverviewTitle}</p>
            <div className="mt-3 flex items-center space-x-2">
              <div className="w-1.5 h-1.5 bg-green-300 rounded-full animate-pulse"></div>
              <span className="text-green-200 text-xs lg:text-sm">Dashboard Active</span>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 lg:gap-6">
          <div className="group bg-gradient-to-br from-white via-blue-50/30 to-white rounded-2xl p-4 lg:p-6 shadow-xl border border-blue-100/50 backdrop-blur-sm transform hover:scale-[1.02] hover:shadow-2xl transition-all duration-300 cursor-pointer">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="relative p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg group-hover:shadow-xl transition-all duration-200 group-hover:scale-105">
                  <FiCalendar className="w-5 h-5 lg:w-6 lg:h-6 text-white" />
                  <div className="absolute inset-0 bg-white/20 rounded-xl"></div>
                </div>
                <div>
                  <p className="text-xs lg:text-sm font-semibold text-gray-500 uppercase tracking-wide">{branding.content.dashboardNextAppointment}</p>
                  <p className="text-lg lg:text-xl font-bold text-gray-800 mt-1">
                    {nextAppointment ? formatDate(nextAppointment.date) : 'None scheduled'}
                  </p>
                </div>
              </div>
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
            </div>
          </div>

          <div className="group bg-gradient-to-br from-white via-green-50/30 to-white rounded-2xl p-4 lg:p-6 shadow-xl border border-green-100/50 backdrop-blur-sm transform hover:scale-[1.02] hover:shadow-2xl transition-all duration-300 cursor-pointer">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="relative p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg group-hover:shadow-xl transition-all duration-200 group-hover:scale-105">
                  <FiShoppingBag className="w-5 h-5 lg:w-6 lg:h-6 text-white" />
                  <div className="absolute inset-0 bg-white/20 rounded-xl"></div>
                </div>
                <div>
                  <p className="text-xs lg:text-sm font-semibold text-gray-500 uppercase tracking-wide">{branding.content.dashboardOrdersTitle}</p>
                  <p className="text-lg lg:text-xl font-bold text-gray-800 mt-1">{orders.length}</p>
                </div>
              </div>
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            </div>
          </div>

          <div className="group bg-gradient-to-br from-white via-purple-50/30 to-white rounded-2xl p-4 lg:p-6 shadow-xl border border-purple-100/50 backdrop-blur-sm transform hover:scale-[1.02] hover:shadow-2xl transition-all duration-300 cursor-pointer">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="relative p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg group-hover:shadow-xl transition-all duration-200 group-hover:scale-105">
                  <FiHeart className="w-5 h-5 lg:w-6 lg:h-6 text-white" />
                  <div className="absolute inset-0 bg-white/20 rounded-xl"></div>
                </div>
                <div>
                  <p className="text-xs lg:text-sm font-semibold text-gray-500 uppercase tracking-wide">{branding.content.dashboardFavoritesTitle}</p>
                  <p className="text-lg lg:text-xl font-bold text-gray-800 mt-1">{favorites.length}</p>
                </div>
              </div>
              <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-gradient-to-br from-white via-gray-50/30 to-white rounded-2xl p-4 lg:p-6 shadow-xl border border-gray-100/50 backdrop-blur-sm">
          <div className="flex items-center space-x-3 mb-4 lg:mb-6">
            <div className="p-2 lg:p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl shadow-lg">
              <FiClock className="w-4 h-4 lg:w-5 lg:h-5 text-white" />
            </div>
            <h3 className="text-lg lg:text-xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">Recent Activity</h3>
          </div>
          <div className="space-y-3">
            {appointments.length > 0 ? (
              appointments.slice(0, 2).map((appointment, index) => (
                <div key={appointment._id || appointment.id || index} className="group flex items-center justify-between p-4 lg:p-5 bg-gradient-to-r from-white to-gray-50/50 rounded-xl shadow-md border border-gray-100/50 hover:shadow-lg transform hover:scale-[1.01] transition-all duration-200">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 lg:p-3 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-lg group-hover:shadow-md transition-all duration-200">
                      <FiCalendar className="w-4 h-4 lg:w-5 lg:h-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-bold text-gray-800 text-sm lg:text-base">{appointment.service?.name || appointment.serviceName || 'Service'}</p>
                      <p className="text-xs lg:text-sm text-gray-500 font-medium">
                        {formatDate(appointment.date)} at {appointment.time}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-3 py-1 rounded-lg text-xs font-bold shadow-md ${getStatusColor(appointment.status)}`}>
                      {appointment.status}
                    </span>
                    <div className="w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse"></div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 lg:py-12">
                <div className="p-3 lg:p-4 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl w-12 h-12 lg:w-16 lg:h-16 mx-auto mb-3 lg:mb-4 flex items-center justify-center">
                  <FiCalendar className="w-6 h-6 lg:w-8 lg:h-8 text-gray-400" />
                </div>
                <p className="text-gray-500 text-base lg:text-lg font-medium">No recent appointments</p>
                <p className="text-gray-400 text-xs lg:text-sm mt-1">Your activity will appear here</p>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  const renderAppointments = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">My Appointments</h2>
        <button
          onClick={() => {
            setShowAppointmentModal(true)
            setModalType('add')
            setEditingAppointment(null)
          }}
          className="text-white px-4 py-2 rounded-lg transition-colors duration-200 cursor-pointer"
          style={{ backgroundColor: branding.colors.secondary }}
          onMouseEnter={(e) => e.target.style.backgroundColor = branding.colors.accent}
          onMouseLeave={(e) => e.target.style.backgroundColor = branding.colors.secondary}
        >
          Book New Appointment
        </button>
      </div>

      {sectionLoading.appointments ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
          <span className="ml-3 text-gray-600">Loading appointments...</span>
        </div>
      ) : (
        <div className="space-y-6">
          {appointments.length > 0 ? (
          appointments.map((appointment) => (
            <div key={appointment._id || appointment.id} className="group bg-gradient-to-br from-white via-gray-50/30 to-white rounded-2xl p-6 shadow-xl border border-gray-100/50 backdrop-blur-sm transform hover:scale-[1.02] hover:shadow-2xl transition-all duration-300">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="relative p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg group-hover:shadow-xl transition-all duration-200 group-hover:scale-105">
                      <FiCalendar className="w-5 h-5 text-white" />
                      <div className="absolute inset-0 bg-white/20 rounded-xl"></div>
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-800 group-hover:text-blue-600 transition-colors duration-200">
                        {appointment.service?.name || appointment.serviceName || 'Service'}
                      </h3>
                      <p className="text-sm text-gray-500 font-medium">
                        {appointment.service?.category || 'Hair Care Service'}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg">
                        <FiCalendar className="w-4 h-4 text-blue-600" />
                        <span className="text-sm font-medium text-gray-700">
                          {appointment.date ? new Date(appointment.date).toLocaleDateString('en-US', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          }) : 'Date not set'}
                        </span>
                      </div>
                      <div className="flex items-center space-x-3 p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg">
                        <FiClock className="w-4 h-4 text-green-600" />
                        <span className="text-sm font-medium text-gray-700">
                          {appointment.time || 'Time not set'}
                        </span>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center space-x-3 p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg">
                        <FiUser className="w-4 h-4 text-purple-600" />
                        <span className="text-sm font-medium text-gray-700">
                          {appointment.stylist || 'Stylist not assigned'}
                        </span>
                      </div>
                      <div className="flex items-center space-x-3 p-3 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg">
                        <span className="text-sm font-bold text-gray-700">
                          Duration: {appointment.service?.duration || 60} mins
                        </span>
                      </div>
                    </div>
                  </div>

                  {appointment.message && (
                    <div className="p-3 bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg mb-4">
                      <p className="text-sm text-gray-600 italic">"{appointment.message}"</p>
                    </div>
                  )}
                </div>

                <div className="text-right ml-6">
                  <span className={`px-4 py-2 rounded-full text-sm font-bold shadow-lg ${getStatusColor(appointment.status)} mb-3 inline-block`}>
                    {appointment.status?.charAt(0).toUpperCase() + appointment.status?.slice(1)}
                  </span>
                  <p className="text-2xl font-bold text-gray-800 mb-4">
                    ${appointment.service?.price || appointment.price || '0'}
                  </p>

                  {/* Action Buttons */}
                  <div className="flex flex-col space-y-2">
                    {appointment.status !== 'completed' && appointment.status !== 'cancelled' && (
                      <>
                        <button
                          onClick={() => handleEditAppointment(appointment)}
                          className="flex items-center justify-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl cursor-pointer"
                        >
                          <FiEdit3 className="w-4 h-4" />
                          <span className="text-sm font-medium">Edit</span>
                        </button>
                        <button
                          onClick={() => handleCancelAppointment(appointment)}
                          className="flex items-center justify-center space-x-2 px-4 py-2 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg hover:from-red-600 hover:to-red-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl cursor-pointer"
                        >
                          <FiX className="w-4 h-4" />
                          <span className="text-sm font-medium">Cancel</span>
                        </button>
                      </>
                    )}
                    <button
                      onClick={() => handleViewAppointment(appointment)}
                      className="flex items-center justify-center space-x-2 px-4 py-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-lg hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl cursor-pointer"
                    >
                      <FiEye className="w-4 h-4" />
                      <span className="text-sm font-medium">View</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="bg-gradient-to-br from-white via-blue-50/30 to-white rounded-2xl p-8 shadow-xl border border-blue-100/50 backdrop-blur-sm text-center">
            <div className="p-6 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-2xl w-20 h-20 mx-auto mb-6 flex items-center justify-center shadow-lg">
              <FiCalendar className="w-10 h-10 text-blue-600" />
            </div>
            <h3 className="text-2xl font-bold text-gray-800 mb-3">No Appointments Yet</h3>
            <p className="text-gray-600 mb-6 text-lg">You haven't booked any appointments yet. Start your hair care journey today!</p>
            <button
              onClick={() => {
                setShowAppointmentModal(true)
                setModalType('add')
                setEditingAppointment(null)
              }}
              className="text-white px-8 py-4 rounded-xl transition-all duration-200 cursor-pointer font-bold text-lg shadow-lg hover:shadow-xl transform hover:scale-105"
              style={{ backgroundColor: branding.colors.secondary }}
              onMouseEnter={(e) => e.target.style.backgroundColor = branding.colors.accent}
              onMouseLeave={(e) => e.target.style.backgroundColor = branding.colors.secondary}
            >
              Book Your First Appointment
            </button>
          </div>
        )}
        </div>
      )}
    </div>
  )

  const renderOrders = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Order History</h2>
        <button
          onClick={() => onNavigate('shop')}
          className="text-white px-4 py-2 rounded-lg transition-colors duration-200 cursor-pointer"
          style={{ backgroundColor: branding.colors.secondary }}
          onMouseEnter={(e) => e.target.style.backgroundColor = branding.colors.accent}
          onMouseLeave={(e) => e.target.style.backgroundColor = branding.colors.secondary}
        >
          Shop Now
        </button>
      </div>

      {sectionLoading.orders ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
          <span className="ml-3 text-gray-600">Loading orders...</span>
        </div>
      ) : (
        <div className="space-y-4">
          {orders.length > 0 ? (
          orders.map((order) => (
            <div key={order.id} className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold mb-2">Order #{order.id}</h3>
                  <p className="text-sm text-gray-600 mb-2">Ordered on {order.date}</p>
                  <div className="space-y-1">
                    {order.items.map((item, index) => (
                      <p key={index} className="text-sm text-gray-700">• {item}</p>
                    ))}
                  </div>
                </div>
                <div className="text-right">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                    {order.status}
                  </span>
                  <p className="text-lg font-semibold mt-2">${order.total}</p>
                  <button
                    className="text-sm mt-2 flex items-center transition-colors duration-200"
                    style={{ color: '#008000' }}
                    onMouseEnter={(e) => e.target.style.color = '#006600'}
                    onMouseLeave={(e) => e.target.style.color = '#008000'}
                  >
                    <FiEye className="w-4 h-4 mr-1" />
                    View Details
                  </button>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="bg-white rounded-xl p-8 shadow-sm text-center">
            <div className="p-4 bg-gradient-to-br from-green-100 to-emerald-100 rounded-2xl w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <FiShoppingBag className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">No Orders Yet</h3>
            <p className="text-gray-600 mb-4">You haven't placed any orders yet. Explore our premium hair care products!</p>
            <button
              onClick={() => onNavigate('shop')}
              className="text-white px-6 py-3 rounded-lg transition-colors duration-200 cursor-pointer font-medium"
              style={{ backgroundColor: branding.colors.secondary }}
              onMouseEnter={(e) => e.target.style.backgroundColor = branding.colors.accent}
              onMouseLeave={(e) => e.target.style.backgroundColor = branding.colors.secondary}
            >
              Start Shopping
            </button>
          </div>
        )}
        </div>
      )}
    </div>
  )

  const renderFavorites = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Favorite Products</h2>
        <button
          onClick={() => onNavigate('shop')}
          className="text-white px-4 py-2 rounded-lg transition-colors duration-200 cursor-pointer"
          style={{ backgroundColor: branding.colors.secondary }}
          onMouseEnter={(e) => e.target.style.backgroundColor = branding.colors.accent}
          onMouseLeave={(e) => e.target.style.backgroundColor = branding.colors.secondary}
        >
          Browse Products
        </button>
      </div>

      {sectionLoading.favorites ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
          <span className="ml-3 text-gray-600">Loading favorites...</span>
        </div>
      ) : favorites.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {favorites.map((product) => (
            <div key={product.id} className="bg-white rounded-xl overflow-hidden shadow-sm cursor-pointer transform hover:scale-[1.02] transition-all duration-200">
              <img
                src={product.image}
                alt={product.name}
                className="w-full h-48 object-cover"
              />
              <div className="p-4">
                <h3 className="font-semibold mb-2">{product.name}</h3>
                <div className="flex justify-between items-center">
                  <span className="text-lg font-bold" style={{ color: branding.colors.secondary }}>${product.price}</span>
                  <button
                    onClick={() => addToCart(product.id)}
                    disabled={isUpdating}
                    className="text-white px-4 py-2 rounded-lg transition-colors duration-200 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                    style={{ backgroundColor: branding.colors.secondary }}
                    onMouseEnter={(e) => !isUpdating && (e.target.style.backgroundColor = branding.colors.accent)}
                    onMouseLeave={(e) => !isUpdating && (e.target.style.backgroundColor = branding.colors.secondary)}
                  >
                    {isUpdating ? 'Adding...' : 'Add to Cart'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-xl p-8 shadow-sm text-center">
          <div className="p-4 bg-gradient-to-br from-purple-100 to-pink-100 rounded-2xl w-16 h-16 mx-auto mb-4 flex items-center justify-center">
            <FiHeart className="w-8 h-8 text-purple-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">No Favorite Products Yet</h3>
          <p className="text-gray-600 mb-4">Start adding products to your favorites to see them here. Discover our amazing hair care collection!</p>
          <button
            onClick={() => onNavigate('shop')}
            className="text-white px-6 py-3 rounded-lg transition-colors duration-200 cursor-pointer font-medium"
            style={{ backgroundColor: branding.colors.secondary }}
            onMouseEnter={(e) => e.target.style.backgroundColor = branding.colors.accent}
            onMouseLeave={(e) => e.target.style.backgroundColor = branding.colors.secondary}
          >
            Explore Products
          </button>
        </div>
      )}
    </div>
  )

  const renderProfile = () => {
    // Show loading state if userData is not available
    if (!userData) {
      return (
        <div className="space-y-6">
          <h2 className="text-2xl font-bold">Profile Settings</h2>
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
            <span className="ml-3 text-gray-600">Loading profile...</span>
          </div>
        </div>
      )
    }

    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Profile Settings</h2>

        <div className="bg-white rounded-xl p-6 shadow-sm">
          <div className="flex items-center space-x-6 mb-6">
            <img
              src={userData?.avatar || userData?.profilePicture || FALLBACK_IMAGES.profile}
              alt={userData?.firstName || userData?.name || 'User'}
              className="w-20 h-20 rounded-full object-cover"
              onError={(e) => {
                e.target.src = FALLBACK_IMAGES.profile
              }}
            />
            <div>
              <h3 className="text-xl font-semibold">
                {userData?.firstName ? `${userData.firstName} ${userData.lastName || ''}` : userData?.name || 'User'}
              </h3>
              <p className="text-gray-600">
                Member since {userData?.joinDate || userData?.createdAt ? new Date(userData.joinDate || userData.createdAt).toLocaleDateString() : 'Recently'}
              </p>
            </div>
          </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
            <input
              type="text"
              value={profileData.firstName}
              onChange={(e) => handleProfileChange('firstName', e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg transition-colors duration-200"
              onFocus={(e) => {
                e.target.style.outline = 'none'
                e.target.style.borderColor = branding.colors.secondary
                e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#d1d5db'
                e.target.style.boxShadow = 'none'
              }}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
            <input
              type="text"
              value={profileData.lastName}
              onChange={(e) => handleProfileChange('lastName', e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg transition-colors duration-200"
              onFocus={(e) => {
                e.target.style.outline = 'none'
                e.target.style.borderColor = branding.colors.secondary
                e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#d1d5db'
                e.target.style.boxShadow = 'none'
              }}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
            <input
              type="email"
              value={profileData.email}
              onChange={(e) => handleProfileChange('email', e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg transition-colors duration-200"
              onFocus={(e) => {
                e.target.style.outline = 'none'
                e.target.style.borderColor = branding.colors.secondary
                e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#d1d5db'
                e.target.style.boxShadow = 'none'
              }}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
            <input
              type="tel"
              value={profileData.phone}
              onChange={(e) => handleProfileChange('phone', e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg transition-colors duration-200"
              onFocus={(e) => {
                e.target.style.outline = 'none'
                e.target.style.borderColor = branding.colors.secondary
                e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#d1d5db'
                e.target.style.boxShadow = 'none'
              }}
            />
          </div>
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">Password</label>
            <button
              className="w-full px-4 py-2 border border-gray-300 rounded-lg text-left text-gray-500 hover:bg-gray-50 transition-colors duration-200 cursor-pointer"
              onClick={() => {
                // TODO: Implement password change modal
                window.dispatchEvent(new CustomEvent('show-notification', {
                  detail: {
                    type: 'info',
                    message: 'Password change feature coming soon!'
                  }
                }))
              }}
            >
              Change Password
            </button>
          </div>
        </div>

        <div className="mt-6 flex space-x-4">
          <button
            onClick={handleProfileSave}
            disabled={isUpdating}
            className="text-white px-6 py-2 rounded-lg transition-colors duration-200 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
            style={{ backgroundColor: branding.colors.secondary }}
            onMouseEnter={(e) => !isUpdating && (e.target.style.backgroundColor = branding.colors.accent)}
            onMouseLeave={(e) => !isUpdating && (e.target.style.backgroundColor = branding.colors.secondary)}
          >
            {isUpdating ? 'Saving...' : 'Save Changes'}
          </button>
          <button
            onClick={() => {
              setProfileData({
                firstName: userData?.firstName || '',
                lastName: userData?.lastName || '',
                email: userData?.email || '',
                phone: userData?.phone || ''
              })
            }}
            className="border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors duration-200 cursor-pointer"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
    )
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverview()
      case 'appointments':
        return renderAppointments()
      case 'orders':
        return renderOrders()
      case 'favorites':
        return renderFavorites()
      case 'profile':
        return renderProfile()
      default:
        return renderOverview()
    }
  }

  // Show loading state
  if (isLoading) {
    return <Loading message="Loading your dashboard..." />
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-12">
            <FiAlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Error Loading Dashboard</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors duration-200"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
      <div className="max-w-7xl mx-auto lg:px-4">
        {/* Mobile Header - Only show on mobile */}
        <div className="lg:hidden bg-white shadow-sm px-4 py-2.5 flex items-center justify-between sticky top-0 z-30">
          <h1 className="text-base font-semibold">Dashboard</h1>
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer"
          >
            <FiMenu className="w-5 h-5" />
          </button>
        </div>

        <div className="flex flex-col lg:flex-row">
          {/* Mobile Sidebar Overlay */}
          {isMobileMenuOpen && (
            <div
              className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
              onClick={() => setIsMobileMenuOpen(false)}
            />
          )}

          {/* Desktop Sidebar - Always visible on lg+ */}
          <div className="hidden lg:block lg:w-72 xl:w-80 lg:flex-shrink-0">
            <div className="bg-gradient-to-br from-white via-gray-50 to-white rounded-2xl shadow-xl border border-gray-100/50 backdrop-blur-sm p-6 mt-6 mx-4 transform hover:scale-[1.01] transition-all duration-300 ease-out">
              {/* Glassmorphism overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/80 via-transparent to-white/40 rounded-2xl"></div>

              <div className="relative z-10">
                {/* User Profile Section */}
                <div className="flex items-center space-x-3 mb-6 p-3 bg-gradient-to-r from-purple-50 via-blue-50 to-indigo-50 rounded-xl border border-purple-100/50 shadow-md backdrop-blur-sm">
                  <div className="relative">
                    <img
                      src={userData?.avatar || userData?.profilePicture || FALLBACK_IMAGES.profile}
                      alt={userData?.firstName || userData?.name || 'User'}
                      className="w-12 h-12 rounded-xl object-cover shadow-lg border-2 border-white/80"
                      onError={(e) => {
                        e.target.src = FALLBACK_IMAGES.profile
                      }}
                    />
                    <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white shadow-md"></div>
                  </div>
                  <div>
                    <h3 className="font-bold text-sm bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                      {userData?.firstName ? `${userData.firstName} ${userData.lastName || ''}` : userData?.name || 'User'}
                    </h3>
                    <p className="text-xs text-gray-500 font-medium">Premium Customer</p>
                    <div className="flex items-center mt-1">
                      <div className="w-1.5 h-1.5 bg-green-400 rounded-full mr-1.5 animate-pulse"></div>
                      <span className="text-xs text-green-600 font-semibold">Online</span>
                    </div>
                  </div>
                </div>

                {/* Site Navigation */}
                <div className="mb-6 p-3 bg-gradient-to-r from-gray-50 via-blue-50 to-gray-50 rounded-xl border border-gray-100/50 shadow-sm">
                  <h4 className="text-xs font-semibold text-gray-600 uppercase tracking-wide mb-3">Site Navigation</h4>
                  <div className="space-y-2">
                    <button
                      onClick={() => onNavigate('home')}
                      className="group w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-all duration-200 cursor-pointer hover:bg-white hover:shadow-sm text-gray-700 hover:text-gray-900"
                    >
                      <div className="p-1.5 rounded-md bg-blue-100 group-hover:bg-blue-200 transition-all duration-200">
                        <FiUser className="w-3 h-3 text-blue-600" />
                      </div>
                      <span className="font-medium text-xs">Back to Home</span>
                    </button>
                    <button
                      onClick={() => onNavigate('shop')}
                      className="group w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-all duration-200 cursor-pointer hover:bg-white hover:shadow-sm text-gray-700 hover:text-gray-900"
                    >
                      <div className="p-1.5 rounded-md bg-green-100 group-hover:bg-green-200 transition-all duration-200">
                        <FiShoppingBag className="w-3 h-3 text-green-600" />
                      </div>
                      <span className="font-medium text-xs">Shop</span>
                    </button>
                    <button
                      onClick={() => onNavigate('consultation')}
                      className="group w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-all duration-200 cursor-pointer hover:bg-white hover:shadow-sm text-gray-700 hover:text-gray-900"
                    >
                      <div className="p-1.5 rounded-md bg-purple-100 group-hover:bg-purple-200 transition-all duration-200">
                        <FiCalendar className="w-3 h-3 text-purple-600" />
                      </div>
                      <span className="font-medium text-xs">Book Consultation</span>
                    </button>
                  </div>
                </div>

                {/* Dashboard Navigation */}
                <nav className="space-y-2">
                  <h4 className="text-xs font-semibold text-gray-600 uppercase tracking-wide mb-3 px-2">Dashboard</h4>
                  {tabs.map((tab, index) => {
                    const Icon = tab.icon
                    const isActive = activeTab === tab.id
                    return (
                      <button
                        key={tab.id}
                        onClick={() => handleTabChange(tab.id)}
                        className={`group w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all duration-200 cursor-pointer transform hover:scale-[1.02] hover:shadow-lg ${
                          isActive
                            ? 'text-white shadow-lg border border-white/20'
                            : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-white hover:shadow-md border border-transparent'
                        }`}
                        style={{
                          background: isActive
                            ? `linear-gradient(135deg, ${branding.colors.secondary}, ${branding.colors.accent})`
                            : 'transparent',
                          animationDelay: `${index * 50}ms`
                        }}
                      >
                        <div className={`p-2 rounded-lg transition-all duration-200 ${
                          isActive
                            ? 'bg-white/20 shadow-md'
                            : 'bg-gray-100 group-hover:bg-white group-hover:shadow-sm'
                        }`}>
                          <Icon className={`w-4 h-4 transition-all duration-200 ${
                            isActive ? 'text-white' : 'text-gray-600 group-hover:text-gray-800'
                          }`} />
                        </div>
                        <span className={`font-medium text-sm transition-all duration-200 ${
                          isActive ? 'text-white' : 'text-gray-700 group-hover:text-gray-900'
                        }`}>
                          {tab.name}
                        </span>
                        {isActive && (
                          <div className="ml-auto w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
                        )}
                      </button>
                    )
                  })}
                </nav>

                {/* Admin Access & Logout Section */}
                <div className="mt-6 pt-4 border-t border-gray-200/50 space-y-2">
                  {/* Admin Dashboard Access - Only show if user has admin privileges */}
                  {userData?.role === 'admin' && (
                    <button
                      onClick={() => onNavigate('admin-dashboard')}
                      className="group w-full flex items-center space-x-3 px-4 py-3 text-blue-600 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 rounded-xl transition-all duration-200 cursor-pointer transform hover:scale-[1.02] hover:shadow-lg border border-transparent hover:border-blue-100"
                    >
                      <div className="p-2 bg-blue-100 group-hover:bg-blue-200 rounded-lg transition-all duration-200 group-hover:shadow-sm">
                        <FiUser className="w-4 h-4 text-blue-600" />
                      </div>
                      <span className="font-medium text-sm">Admin Dashboard</span>
                    </button>
                  )}

                  <button
                    onClick={onLogout}
                    className="group w-full flex items-center space-x-3 px-4 py-3 text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 rounded-xl transition-all duration-200 cursor-pointer transform hover:scale-[1.02] hover:shadow-lg border border-transparent hover:border-red-100"
                  >
                    <div className="p-2 bg-red-100 group-hover:bg-red-200 rounded-lg transition-all duration-200 group-hover:shadow-sm">
                      <FiLogOut className="w-4 h-4 text-red-600" />
                    </div>
                    <span className="font-medium text-sm">Logout</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Mobile Menu Popup - Center modal on mobile */}
          {isMobileMenuOpen && (
            <div className="lg:hidden fixed inset-0 z-50 flex items-center justify-center p-4">
              {/* Modal backdrop */}
              <div
                className="absolute inset-0 bg-black bg-opacity-40 backdrop-blur-sm"
                onClick={() => setIsMobileMenuOpen(false)}
              />

              {/* Modal content */}
              <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-sm mx-auto transform transition-all duration-300 ease-out max-h-[90vh] overflow-hidden">
                {/* Header with close button */}
                <div className="flex items-center justify-between p-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white">
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <img
                        src={userData?.avatar || userData?.profilePicture || FALLBACK_IMAGES.profile}
                        alt={userData?.firstName || userData?.name || 'User'}
                        className="w-10 h-10 rounded-xl object-cover shadow-md border-2 border-white"
                        onError={(e) => {
                          e.target.src = FALLBACK_IMAGES.profile
                        }}
                      />
                      <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white"></div>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 text-sm">
                        {userData?.firstName ? `${userData.firstName} ${userData.lastName || ''}` : userData?.name || 'User'}
                      </h3>
                      <p className="text-xs text-gray-500">Premium Customer</p>
                    </div>
                  </div>
                  <button
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer"
                  >
                    <FiX className="w-5 h-5 text-gray-500" />
                  </button>
                </div>

                {/* Navigation menu */}
                <div className="p-4 max-h-[calc(90vh-120px)] overflow-y-auto">
                  {/* Site Navigation */}
                  <div className="mb-5">
                    <div className="flex items-center space-x-2 mb-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <h4 className="text-xs font-bold text-gray-600 uppercase tracking-wide">Site Navigation</h4>
                    </div>
                    <div className="space-y-1">
                      <button
                        onClick={() => {
                          onNavigate('home')
                          setIsMobileMenuOpen(false)
                        }}
                        className="w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left transition-all duration-200 cursor-pointer text-gray-700 hover:bg-blue-50 hover:text-blue-700 group"
                      >
                        <div className="p-1.5 rounded-md bg-blue-100 group-hover:bg-blue-200 transition-colors duration-200">
                          <FiUser className="w-3.5 h-3.5 text-blue-600" />
                        </div>
                        <span className="font-medium text-sm">Back to Home</span>
                      </button>
                      <button
                        onClick={() => {
                          onNavigate('shop')
                          setIsMobileMenuOpen(false)
                        }}
                        className="w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left transition-all duration-200 cursor-pointer text-gray-700 hover:bg-green-50 hover:text-green-700 group"
                      >
                        <div className="p-1.5 rounded-md bg-green-100 group-hover:bg-green-200 transition-colors duration-200">
                          <FiShoppingBag className="w-3.5 h-3.5 text-green-600" />
                        </div>
                        <span className="font-medium text-sm">Shop</span>
                      </button>
                      <button
                        onClick={() => {
                          onNavigate('consultation')
                          setIsMobileMenuOpen(false)
                        }}
                        className="w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left transition-all duration-200 cursor-pointer text-gray-700 hover:bg-purple-50 hover:text-purple-700 group"
                      >
                        <div className="p-1.5 rounded-md bg-purple-100 group-hover:bg-purple-200 transition-colors duration-200">
                          <FiCalendar className="w-3.5 h-3.5 text-purple-600" />
                        </div>
                        <span className="font-medium text-sm">Book Consultation</span>
                      </button>
                    </div>
                  </div>

                  {/* Dashboard Navigation */}
                  <div>
                    <div className="flex items-center space-x-2 mb-3">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                      <h4 className="text-xs font-bold text-gray-600 uppercase tracking-wide">Dashboard</h4>
                    </div>
                    <nav className="space-y-1">
                      {tabs.map((tab) => {
                        const Icon = tab.icon
                        const isActive = activeTab === tab.id
                        return (
                          <button
                            key={tab.id}
                            onClick={() => {
                              handleTabChange(tab.id)
                              setIsMobileMenuOpen(false) // Close mobile menu on selection
                            }}
                            className={`w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left transition-all duration-200 cursor-pointer ${
                              isActive
                                ? 'text-white shadow-md transform scale-[1.02]'
                                : 'text-gray-700 hover:bg-gray-50 hover:transform hover:scale-[1.01]'
                            }`}
                            style={isActive ? { backgroundColor: branding.colors.secondary } : {}}
                          >
                            <div className={`p-1.5 rounded-md transition-colors duration-200 ${
                              isActive ? 'bg-white/20' : 'bg-gray-100'
                            }`}>
                              <Icon className={`w-3.5 h-3.5 ${isActive ? 'text-white' : 'text-gray-600'}`} />
                            </div>
                            <span className="font-medium text-sm">{tab.name}</span>
                            {isActive && (
                              <div className="ml-auto w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
                            )}
                          </button>
                        )
                      })}
                    </nav>
                  </div>

                  {/* Admin Access & Logout buttons */}
                  <div className="mt-5 pt-4 border-t border-gray-100">
                    <div className="space-y-1">
                      {/* Admin Dashboard Access - Only show if user has admin privileges */}
                      {userData?.role === 'admin' && (
                        <button
                          onClick={() => {
                            onNavigate('admin-dashboard')
                            setIsMobileMenuOpen(false)
                          }}
                          className="w-full flex items-center space-x-3 px-3 py-2.5 text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 cursor-pointer hover:transform hover:scale-[1.01] group"
                        >
                          <div className="p-1.5 rounded-md bg-blue-100 group-hover:bg-blue-200 transition-colors duration-200">
                            <FiUser className="w-3.5 h-3.5 text-blue-600" />
                          </div>
                          <span className="font-medium text-sm">Admin Dashboard</span>
                        </button>
                      )}

                      <button
                        onClick={() => {
                          onLogout()
                          setIsMobileMenuOpen(false)
                        }}
                        className="w-full flex items-center space-x-3 px-3 py-2.5 text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200 cursor-pointer hover:transform hover:scale-[1.01] group"
                      >
                        <div className="p-1.5 rounded-md bg-red-100 group-hover:bg-red-200 transition-colors duration-200">
                          <FiLogOut className="w-3.5 h-3.5 text-red-600" />
                        </div>
                        <span className="font-medium text-sm">Logout</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}


          {/* Main Content */}
          <div className="flex-1 p-3 lg:p-6 lg:pl-3">
            <div className="max-w-6xl mx-auto">
              {renderContent()}
            </div>
          </div>
        </div>
      </div>

      {/* Appointment Modal */}
      <AppointmentModal
        isOpen={showAppointmentModal}
        onClose={() => {
          setShowAppointmentModal(false)
          setEditingAppointment(null)
          setModalType('add')
        }}
        onSave={handleSaveAppointment}
        editingItem={editingAppointment}
        modalType={modalType}
        branding={branding}
        currentUser={userData}
        isUserMode={true}
      />
    </div>
  )
}

export default UserDashboard
