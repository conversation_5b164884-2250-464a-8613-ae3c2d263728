import React, { useState, useMemo } from 'react'
import { FiCalendar, Fi<PERSON>ser, <PERSON>Eye, FiEdit3, FiTrash2, FiPlus, FiSearch, FiFilter } from 'react-icons/fi'
import AppointmentModal from '../../../components/Modals/AppointmentModal'
import AppointmentDetailModal from '../../../components/Modals/AppointmentDetailModal'
import { adminService } from '../../../services'
import { useDebouncedSearch } from '../../../hooks/useDebounce'
import { searchAppointments } from '../../../utils/searchUtils'

const AdminAppointments = ({
  appointments,
  sectionLoading,
  searchTerm,
  setSearchTerm,
  statusFilter,
  setStatusFilter,
  showAddModal,
  setShowAddModal,
  modalType,
  setModalType,
  editingItem,
  setEditingItem,
  setViewingItem,
  showToast,
  setConfirmDialog,
  handleDeleteAppointment,
  branding,
  refreshData
}) => {
  // State for detail modal
  const [showDetailModal, setShowDetailModal] = useState(false)
  const [detailItem, setDetailItem] = useState(null)
  // branding is now passed as a prop

  // Ensure appointments is always an array - handle backend response structure
  const appointmentsArray = Array.isArray(appointments)
    ? appointments
    : (appointments?.appointments && Array.isArray(appointments.appointments))
      ? appointments.appointments
    : (appointments?.data?.appointments && Array.isArray(appointments.data.appointments))
      ? appointments.data.appointments
      : (appointments?.data && Array.isArray(appointments.data))
        ? appointments.data
        : []

  // Use debounced search for better performance
  const debouncedSearchTerm = useDebouncedSearch(searchTerm, 150)

  // Use memoized filtering for better performance
  const filteredAppointments = useMemo(() => {
    // First apply search filter using the comprehensive search utility
    let filtered = searchAppointments(appointmentsArray, debouncedSearchTerm)

    // Then apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(appointment => appointment.status === statusFilter)
    }

    return filtered
  }, [appointmentsArray, debouncedSearchTerm, statusFilter])



  // Set up global function for switching from view to edit mode
  React.useEffect(() => {
    window.switchToEditMode = (appointment) => {
      setEditingItem(appointment)
      setModalType('edit')
      setShowAddModal(true)
    }

    return () => {
      delete window.switchToEditMode
    }
  }, [setEditingItem, setModalType, setShowAddModal])

  // Handlers for detail modal
  const handleViewDetails = (appointment) => {
    setDetailItem(appointment)
    setShowDetailModal(true)
  }

  const handleEditFromDetail = (appointment) => {
    setShowDetailModal(false)
    setEditingItem(appointment)
    setModalType('edit')
    setShowAddModal(true)
  }

  const handleDeleteFromDetail = async (appointmentId) => {
    await handleDeleteAppointment(appointmentId)
    setShowDetailModal(false)
    refreshData()
  }

  // Helper function to format duration from minutes to readable format
  const formatDuration = (minutes) => {
    if (!minutes) return 'Duration not set'
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60

    if (hours === 0) {
      return `${mins} min${mins !== 1 ? 's' : ''}`
    } else if (mins === 0) {
      return `${hours} hour${hours !== 1 ? 's' : ''}`
    } else {
      return `${hours}h ${mins}m`
    }
  }

  // Helper function to format time from 24-hour to 12-hour format
  const formatTime = (time24) => {
    if (!time24) return 'Time not set'

    // Handle both HH:MM and HH:MM:SS formats
    const timeParts = time24.split(':')
    if (timeParts.length < 2) return time24

    let hours = parseInt(timeParts[0])
    const minutes = timeParts[1]
    const ampm = hours >= 12 ? 'PM' : 'AM'

    // Convert to 12-hour format
    hours = hours % 12
    hours = hours ? hours : 12 // 0 should be 12

    return `${hours}:${minutes} ${ampm}`
  }

  // Handle save appointment (create or update)
  const handleSaveAppointment = async (appointmentData) => {
    try {
      if (modalType === 'edit' && editingItem) {
        // Update existing appointment
        await adminService.updateAppointment(editingItem._id, appointmentData)
        showToast('Appointment updated successfully!', 'success')
      } else {
        // Transform data for backend API
        const backendData = {
          firstName: appointmentData.firstName,
          lastName: appointmentData.lastName,
          email: appointmentData.customerEmail,
          phone: appointmentData.customerPhone,
          userId: appointmentData.userId === 'temp-user' ? 'temp-user' : appointmentData.userId,
          service: appointmentData.service,
          date: appointmentData.date,
          time: appointmentData.time,
          message: appointmentData.notes
        }

        // Create new appointment
        await adminService.createAppointment(backendData)
        showToast('Appointment created successfully!', 'success')
      }

      // Close modal and refresh data
      setShowAddModal(false)
      setEditingItem(null)
      setModalType('')
      if (refreshData) {
        refreshData()
      }
    } catch (error) {
      console.error('Error saving appointment:', error)
      showToast('Failed to save appointment', 'error')
      throw error // Re-throw to prevent modal from closing
    }
  }

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'confirmed': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      case 'completed': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Appointments Management</h2>
          <p className="text-gray-600 mt-1">Manage all customer appointments and bookings</p>
        </div>
        <button
          onClick={() => {
            setShowAddModal(true)
            setModalType('add')
            setEditingItem(null)
          }}
          className="flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
        >
          <FiPlus className="w-5 h-5 mr-2" />
          Add Appointment
        </button>
      </div>

      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden">
        {/* Search and Filter */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search appointments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="relative">
              <FiFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="confirmed">Confirmed</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
          </div>
        </div>

        {false && sectionLoading?.appointments ? (
          <div className="p-6">
            <div className="space-y-4">
              {[...Array(5)].map((_, index) => (
                <div key={index} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-xl animate-pulse">
                  <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-300 rounded w-32"></div>
                    <div className="h-3 bg-gray-300 rounded w-24"></div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-300 rounded w-16"></div>
                    <div className="h-6 bg-gray-300 rounded w-20"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : filteredAppointments.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Service
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date & Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contact
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredAppointments.map((appointment, index) => (
                  <tr key={appointment._id || index} className="hover:bg-gray-50 transition-colors duration-200">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center"
                             style={{ background: `linear-gradient(135deg, ${branding.colors.secondary}20, ${branding.colors.accent}20)` }}>
                          <FiUser className="w-5 h-5" style={{ color: branding.colors.secondary }} />
                        </div>
                        <div className="ml-4">
                          <div className="font-semibold text-gray-900">
                            {appointment.customerInfo?.name || appointment.user?.name || 'Unknown Customer'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {appointment.service?.name || appointment.service || 'Service'}
                      </div>
                      {appointment.service?.price && (
                        <div className="text-xs text-gray-500">
                          ${appointment.service.price} • {formatDuration(appointment.service.duration)}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {appointment.date ? new Date(appointment.date).toLocaleDateString() : 'Date not set'}
                      </div>
                      <div className="text-sm text-gray-500">
                        {formatTime(appointment.time)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        appointment.status === 'confirmed' 
                          ? 'bg-green-100 text-green-800'
                          : appointment.status === 'completed'
                          ? 'bg-blue-100 text-blue-800'
                          : appointment.status === 'cancelled'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {appointment.status || 'Pending'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>{appointment.customerInfo?.email || appointment.user?.email}</div>
                      <div className="text-gray-500">{appointment.customerInfo?.phone || appointment.user?.phone}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => handleViewDetails(appointment)}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 cursor-pointer"
                          title="View Details"
                        >
                          <FiEye className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => {
                            setEditingItem(appointment)
                            setModalType('edit')
                            setShowAddModal(true)
                          }}
                          className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200 cursor-pointer"
                          title="Edit"
                        >
                          <FiEdit3 className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => {
                            setConfirmDialog({
                              title: 'Delete Appointment',
                              message: `Are you sure you want to delete the appointment for ${appointment.customerName || appointment.name || 'this customer'}?`,
                              onConfirm: () => handleDeleteAppointment(appointment.id || index)
                            })
                          }}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 cursor-pointer"
                          title="Delete"
                        >
                          <FiTrash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-12">
            <FiCalendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No appointments found</h3>
            <p className="text-gray-600 mb-6">
              {searchTerm || statusFilter !== 'all' 
                ? 'Try adjusting your search or filter criteria.'
                : 'Get started by adding your first appointment.'
              }
            </p>
            {(!searchTerm && statusFilter === 'all') && (
              <button
                onClick={() => {
                  setShowAddModal(true)
                  setModalType('add')
                  setEditingItem(null)
                }}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 cursor-pointer"
              >
                <FiPlus className="w-4 h-4 mr-2" />
                Add First Appointment
              </button>
            )}
          </div>
        )}
      </div>

      {/* Appointment Modal */}
      <AppointmentModal
        isOpen={showAddModal}
        onClose={() => {
          setShowAddModal(false)
          setEditingItem(null)
          setModalType('')
        }}
        onSave={handleSaveAppointment}
        editingItem={editingItem}
        modalType={modalType}
        branding={branding}
      />

      {/* Appointment Detail Modal */}
      <AppointmentDetailModal
        appointment={detailItem}
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        onEdit={handleEditFromDetail}
        onDelete={handleDeleteFromDetail}
        showToast={showToast}
      />
    </div>
  )
}

export default AdminAppointments
