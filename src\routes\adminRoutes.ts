import { Router } from 'express';
import { Admin<PERSON><PERSON><PERSON>er, ProductController, ServiceController } from '../controllers';
import { authenticate, authorize } from '../middleware/auth';
import { validate } from '../middleware/validation';
import { paginationValidation, mongoIdValidation } from '../utils/validation';

const router = Router();

// All admin routes require authentication and admin role
router.use(authenticate);
router.use(authorize('admin'));

// GET /api/admin/dashboard
router.get(
  '/dashboard',
  AdminController.getDashboardStats
);

// GET /api/admin/appointments
router.get(
  '/appointments',
  validate(paginationValidation),
  AdminController.getAppointments
);

// POST /api/admin/appointments
router.post(
  '/appointments',
  AdminController.createAppointment
);

// PUT /api/admin/appointments/:id
router.put(
  '/appointments/:id',
  validate(mongoIdValidation()),
  AdminController.updateAppointment
);

// DELETE /api/admin/appointments/:id
router.delete(
  '/appointments/:id',
  validate(mongoIdValidation()),
  AdminController.deleteAppointment
);

// GET /api/admin/customers
router.get(
  '/customers',
  validate(paginationValidation),
  AdminController.getCustomers
);

// POST /api/admin/customers
router.post(
  '/customers',
  AdminController.createCustomer
);

// PUT /api/admin/customers/:id
router.put(
  '/customers/:id',
  validate(mongoIdValidation()),
  AdminController.updateCustomer
);

// DELETE /api/admin/customers/:id
router.delete(
  '/customers/:id',
  validate(mongoIdValidation()),
  AdminController.deleteCustomer
);

// GET /api/admin/orders
router.get(
  '/orders',
  validate(paginationValidation),
  AdminController.getOrders
);

// PUT /api/admin/orders/:id
router.put(
  '/orders/:id',
  validate(mongoIdValidation()),
  AdminController.updateOrder
);

// DELETE /api/admin/orders/:id
router.delete(
  '/orders/:id',
  validate(mongoIdValidation()),
  AdminController.deleteOrder
);

// GET /api/admin/products
router.get(
  '/products',
  validate(paginationValidation),
  AdminController.getProducts
);

// POST /api/admin/products
router.post(
  '/products',
  ProductController.createProduct
);

// PUT /api/admin/products/:id
router.put(
  '/products/:id',
  validate(mongoIdValidation()),
  ProductController.updateProduct
);

// DELETE /api/admin/products/:id
router.delete(
  '/products/:id',
  validate(mongoIdValidation()),
  ProductController.deleteProduct
);

// GET /api/admin/services
router.get(
  '/services',
  validate(paginationValidation),
  AdminController.getServices
);

// POST /api/admin/services
router.post(
  '/services',
  ServiceController.createService
);

// PUT /api/admin/services/:id
router.put(
  '/services/:id',
  validate(mongoIdValidation()),
  ServiceController.updateService
);

// DELETE /api/admin/services/:id
router.delete(
  '/services/:id',
  validate(mongoIdValidation()),
  ServiceController.deleteService
);

export default router;
